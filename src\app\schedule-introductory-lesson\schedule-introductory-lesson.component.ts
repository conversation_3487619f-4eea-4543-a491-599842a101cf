import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { SchedulerInfoFormComponent } from './pages/scheduler-info-form/scheduler-info-form.component';
import { BaseComponent } from '../shared/components/base-component/base.component';
import { ScheduleLessonBasicInfoComponent } from './pages/schedule-lesson-basic-info/schedule-lesson-basic-info.component';
import { MatButtonModule } from '@angular/material/button';
import { DetailsOnScheduleAppointmentPageComponent } from './pages/details-on-schedule-appointment-page/details-on-schedule-appointment-page.component';
import { AvailableAppointmentListComponent } from './pages/available-appointment-list/available-appointment-list.component';
import { InstructorList, SchedulerInfo, SlotOrStaffDetails } from './models';
import { MatSidenavModule } from '@angular/material/sidenav';
import { InstructorListComponent } from './pages/instructor-list/instructor-list.component';
import { takeUntil } from 'rxjs';
import { API_URL } from '../shared/constants/api-url.constants';
import { CBResponse } from '../shared/models';
import { InstructorService } from './services';
import { DirectivesModule } from '../shared/directives/directives.module';
import { NavigationService } from '../shared/services';
import { ActivatedRoute } from '@angular/router';
import { ROUTER_PATHS } from '../shared/constants';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, MatSidenavModule, DirectivesModule],
  COMPONENTS: [
    SchedulerInfoFormComponent,
    ScheduleLessonBasicInfoComponent,
    DetailsOnScheduleAppointmentPageComponent,
    AvailableAppointmentListComponent,
    InstructorListComponent
  ]
};

@Component({
  selector: 'app-schedule-introductory-lesson',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './schedule-introductory-lesson.component.html',
  styleUrl: './schedule-introductory-lesson.component.scss'
})
export class ScheduleIntroductoryLessonComponent extends BaseComponent implements OnInit {
  @ViewChild(SchedulerInfoFormComponent) schedulerInfoFormComponent!: SchedulerInfoFormComponent;
  @ViewChild(AvailableAppointmentListComponent) availableAppointmentListComponent!: AvailableAppointmentListComponent;

  slotOrStaffDetails!: SlotOrStaffDetails;
  scheduleInfo!: SchedulerInfo;
  totalInstructorsCount!: number;
  isFromLaunchPage = false;

  selectedInstructorsId: Array<number> = [];
  selectedInstructorsIdOriginal: Array<number> = [];

  isInstructorsSideNavOpen = false;
  specialNeed = false;
  isBasicInfoFilled = false;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly instructorService: InstructorService,
    private readonly cdr: ChangeDetectorRef,
    private readonly navigationService: NavigationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.setIsFromLaunchPage();
    this.getInstructorsCount();
  }

  setIsFromLaunchPage(): void {
    this.route.queryParams.subscribe((params: any) => {
      if (params?.origin === 'launch-page') {
        this.isFromLaunchPage = true;
      }
    });
  }

  setBasicInfoFilled(value: boolean): void {
    this.isBasicInfoFilled = value;
  }

  getSchedulerInfoFormValue(): void {
    if (this.schedulerInfoFormComponent.schedulerInfoForm.invalid) {
      this.schedulerInfoFormComponent.schedulerInfoForm.markAllAsTouched();
      return;
    }
    this.schedulerInfoFormComponent.schedulerInfoForm.markAsUntouched();
    this.scheduleInfo = this.schedulerInfoFormComponent.schedulerInfoForm.getRawValue() as SchedulerInfo;
    this.setBasicInfoFilled(true);
  }

  submitSpecialNeedInfo(): void {
    this.schedulerInfoFormComponent.onSubmitSpecialNeedInfo();
  }

  setScheduleAppointmentsDetails(event: SlotOrStaffDetails): void {
    this.slotOrStaffDetails = event;
  }

  toggleInstructorSideNav(event: boolean): void {
    this.isInstructorsSideNavOpen = event;
    if (event) {
      this.selectedInstructorsIdOriginal = JSON.parse(JSON.stringify(this.selectedInstructorsId));
    }
  }

  resetInstructorSideNav(): void {
    this.selectedInstructorsId = this.selectedInstructorsIdOriginal;
  }

  setSelectedInstructorId(event: Array<number>): void {
    this.selectedInstructorsId = event;
    this.availableAppointmentListComponent?.getIntroductoryLessonDetails(this.selectedInstructorsId);
  }

  getInstructorsCount(): void {
    this.instructorService
      .add({ page: 1 }, `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: CBResponse<InstructorList>) => {
          this.totalInstructorsCount = response.result.totalCount;
          this.cdr.detectChanges();
        }
      });
  }

  redirectToSignIn(): void {
    if (this.isFromLaunchPage) {
      this.navigationService.navigate([ROUTER_PATHS.launchPage]);
    }
    else {
      this.navigationService.navigateToLogin();
    }
  }
}
