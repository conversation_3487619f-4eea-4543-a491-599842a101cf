@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.available-appointment-list-wrapper {
  .filter-wrapper {
    @include flex-content-space-between;

    .date-time-slot-wrapper {
      display: flex;
      .date-range {
        margin-right: 8px;
        width: 235px;
      }

      .time-range {
        width: 200px !important;

        input {
          width: 100%;
          height: 45px;
          border: 1px solid $btn-options-border-color;
          border-radius: 4px;
          padding: 10px 12px;
          background-color: white;
          cursor: pointer;

          &:focus {
            outline: none;
            border-color: $primary-color;
          }

          &::placeholder {
            color: $gray-text;
          }
        }
      }
    }

    .select-instructor-wrapper {
      border: 1px solid $btn-options-border-color;
      border-radius: 6px;
      padding: 10px;
      cursor: pointer;
      @include flex-content-space-between;

      .select-instructor-btn,
      mat-icon {
        color: $gray-text;
      }

      mat-icon {
        margin-top: 1px;
      }

      .selected-instructor-count {
        font-weight: 600;
      }
    }

    .time-slot-range {
      width: 235px;
    }
  }

  .appointment-list-wrapper {
    margin-top: 20px;
    font-size: 14px;

    .date-header-wrapper {
      @include flex-content-align-center;
      background-color: $header-schedule-bg-color;
      border-radius: 6px;
      padding: 10px 16px;
      overflow-x: auto;
      scrollbar-width: none;

      .header-content {
        padding: 10px 8px;
        text-align: center;
        min-width: 115px;
        cursor: pointer;
        background-color: $header-schedule-bg-color;

        .date {
          font-weight: 700;
        }

        .day {
          color: $gray-text;
        }
      }

      .schedule-date-active {
        background-color: $white-color;
        border-radius: 4px;
        color: $primary-color;

        .day {
          color: $primary-color !important;
        }
      }
    }

    .date-header-wrapper:hover {
      scrollbar-width: thin !important;
    }

    .time-slot-wrapper {
      overflow: auto;
      height: calc(100vh - 355px);

      .time-slot-content-wrapper {
        @include flex-content-center;
        flex-wrap: wrap;

        .time-slot-content {
          min-width: 187px;
          padding: 10px 16px;
          margin: 12px 10px 0px 0px;
          border: 1px solid $btn-options-border-color;
          border-radius: 6px;
          text-align: center;
          color: $gray-text;
          cursor: pointer;

          .staff-wrapper {
            @include flex-content-center;
            margin-top: 3px;

            .staff-img {
              width: 25px;
              height: 25px;
              border-radius: 50%;
              margin-right: 8px;
            }
          }
        }

        .time-slot-content-active {
          border: 1px solid $primary-color;
          .time-slot {
            color: $primary-color;
            font-weight: 600;
          }

          .staff-wrapper {
            .staff-name {
              color: $black-color;
            }
          }
        }
      }
    }
  }
}

::ng-deep {
  .mat-mdc-text-field-wrapper {
    background-color: white !important;
    border: 1px solid $btn-options-border-color;
  }

  .mat-mdc-form-field-focus-overlay,
  .mat-mdc-text-field-wrapper,
  .mat-mdc-form-field-flex {
    height: 45px;
  }

  .mat-mdc-form-field-infix {
    min-height: 45px !important;
    padding: 10px 0 !important;
  }

  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }

  .mbsc-ios.mbsc-form-control-wrapper:before,
  .mbsc-ios.mbsc-form-control-wrapper:after {
    border-color: $white-color;
  }

  .mbsc-ios.mbsc-textfield {
    height: 0 !important;
  }

  .mbsc-ios.mbsc-form-control-wrapper {
    margin-top: 0px !important;
  }
}

.time-slots-wrapper {
  background: $white-color;
  box-shadow: 0px 0px 30px 0px #0000001a;
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 14px;

  .time-slot-content-wrapper {
    .time-slot-content {
      display: flex;
      .time-slot {
        border: 1px solid $btn-options-border-color;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        margin-right: 8px;
        min-width: 145px;
        text-align: center;
        color: $gray-text;
        padding: 3px;
      }

      .time-slot-active {
        color: $primary-color;
        border-color: $primary-color;
      }
    }
  }

  .action-btn-wrapper {
    @include flex-content-space-between;
    margin-top: 5px;

    .action-btn {
      color: $gray-text;
      cursor: pointer;
    }
  }
}

.no-appointments-available-wrapper {
  margin: auto;
  max-width: 555px;
  text-align: center;
  display: flex;
  align-items: center;
  margin-top: 30px;
  height: -webkit-fill-available;

  .description {
    font-size: 20px;

    .d-bold {
      font-weight: 600;
    }
  }

  .NAAW-time-slots-wrapper {
    margin-top: 20px;

    .time-slot-options {
      @include flex-content-center;
      .time-slot {
        border: 1px solid $btn-options-border-color;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        margin-right: 8px;
        min-width: 145px;
        text-align: center;
        color: $gray-text;
        padding: 3px;
      }

      .time-slot-active {
        color: $primary-color;
        border-color: $primary-color;
      }
    }
  }
}

@media (max-width: 530px) {
  .available-appointment-list-wrapper {
    margin-bottom: 20px;
    .filter-wrapper {
      flex-wrap: wrap;

      .date-time-slot-wrapper {
        margin-bottom: 8px;
        flex-wrap: wrap;
        .date-range {
          margin-bottom: 8px;
        }
      }
    }

    .appointment-list-wrapper {
      height: unset;
      overflow: hidden;

      .no-appointments-available-wrapper {
        .no-appointments-available-content {
          .NAAW-time-slots-wrapper {
            .time-slot-options {
              flex-wrap: wrap;
            }
          }
        }
      }
    }
  }
}
