<app-auth-common-layout>
  <div class="unauthorized-content">
    <ng-container [ngTemplateOutlet]="forgotPasswordFormTemplate"></ng-container>
  </div>
</app-auth-common-layout>

<ng-template #forgotPasswordFormTemplate>
  <p class="title">{{ isFromLaunchPage ? 'Reset' : 'Forgot' }} password ?</p>
  <p class="sub-title">Enter your email to reset your password.</p>
  <div class="mt-20">
    <form [formGroup]="forgotPasswordInitForm" (ngSubmit)="onSubmit()">
      <mat-form-field>
        <mat-label>Email</mat-label>
        <input type="email" matInput formControlName="emailAddress" />
        <mat-error>
          <app-error-messages [control]="forgotPasswordInitForm.controls.emailAddress"></app-error-messages>
        </mat-error>
      </mat-form-field>

      <button
        mat-raised-button
        color="primary"
        class="w-100 mat-primary-btn"
        type="submit"
        [disabled]="forgotPasswordInitForm.invalid"
        [appLoader]="showBtnLoader">
        Submit
      </button>
      <ng-container [ngTemplateOutlet]="isFromLaunchPage ? null : backToLoginLink"></ng-container>
    </form>
  </div>
</ng-template>

<ng-template #backToLoginLink>
  <div class="back-to-sign-in-wrapper">
    <span class="back-to">Back to</span>
    <span class="sign-in" [routerLink]="[path.root, path.auth.root, path.auth.login]">Sign In</span>
  </div>
</ng-template>
