import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { takeUntil } from 'rxjs';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { ForgotPasswordInitFormGroupType } from '../../models';
import { AuthService } from '../../services';
import { AuthCommonLayoutComponent } from '../auth-common-layout/auth-common-layout.component';
import { AppToasterService } from 'src/app/shared/services';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';

const DEPENDENCIES = {
  COMPONENTS: [AuthCommonLayoutComponent],
  MODULES: [
    ReactiveFormsModule,
    MatInputModule,
    SharedModule,
    RouterModule,
    CommonModule,
    MatButtonModule,
    MatFormFieldModule
  ]
};
@Component({
  standalone: true,
  selector: 'app-forgot-password-init',
  templateUrl: './forgot-password-init.component.html',
  styleUrls: ['./forgot-password-init.component.scss'],
  imports: [...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.MODULES],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ForgotPasswordInitComponent extends BaseComponent implements OnInit {
  forgotPasswordInitForm!: FormGroup<ForgotPasswordInitFormGroupType>;
  isFromLaunchPage = false;

  constructor(
    private readonly authService: AuthService,
    private readonly route: ActivatedRoute,
    private readonly toasterService: AppToasterService,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initForgotPasswordForm();
  }

  initForgotPasswordForm(): void {
    this.forgotPasswordInitForm = new FormGroup<ForgotPasswordInitFormGroupType>({
      emailAddress: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.EMAIL)]
      })
    });
    this.setUserEmailFromLoginScreen();
  }

  setUserEmailFromLoginScreen(): void {
    this.route.queryParams.subscribe((params: any) => {
      const email = params?.email;
      if (email) {
        this.forgotPasswordInitForm.patchValue({ emailAddress: email });
      }
      if (params?.origin === 'launch-page') {
        this.isFromLaunchPage = true;
      }
    });
  }

  onSubmit(): void {
    if (this.forgotPasswordInitForm.invalid) {
      this.forgotPasswordInitForm.markAllAsTouched();
      return;
    }
    this.showBtnLoader = true;
    this.forgotPasswordInitForm.markAsUntouched();
    this.authService
      .onForgotPasswordInit(this.forgotPasswordInitForm.getRawValue())
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.toasterService.success(this.constants.successMessages.forgotPasswordInitSuccess);
          if (this.isFromLaunchPage) {
            this.router.navigate([this.path.launchPage]);
          }
          else {
            this.router.navigate([this.path.auth.root, this.path.auth.login]);
          }
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }
}
