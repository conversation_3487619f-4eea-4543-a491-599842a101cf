import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatNativeDateModule, provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { Appointments, IntroductoryLesson, SchedulerInfo, SlotOrStaffDetails } from '../../models';
import { MatIconModule } from '@angular/material/icon';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatButtonModule } from '@angular/material/button';
import { IntroductoryLessonsService } from '../../services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CBResponse } from 'src/app/shared/models';
import moment from 'moment';
import { SharedModule } from 'src/app/shared/shared.module';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { Duration } from 'src/app/pages/settings/pages/plan/models';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { MbscDatepickerModule } from '@mobiscroll/angular';
import { CommonUtils } from 'src/app/shared/utils';
import { MatInputModule } from '@angular/material/input';

const DEPENDENCIES = {
  MODULES: [
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    FormsModule,
    CommonModule,
    MatIconModule,
    OverlayModule,
    MatButtonModule,
    SharedModule,
    MbscDatepickerModule,
    MatInputModule
  ],
  COMPONENTS: []
};

@Component({
  selector: 'app-available-appointment-list',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS],
  providers: [provideNativeDateAdapter()],
  templateUrl: './available-appointment-list.component.html',
  styleUrl: './available-appointment-list.component.scss'
})
export class AvailableAppointmentListComponent extends BaseComponent implements OnInit {
  @Input() scheduleInfo!: SchedulerInfo;
  @Input() selectedInstructorsCount!: string | number;

  startDate!: Date;
  endDate!: Date;
  selectedTimeSlotId!: number;
  startTime!: Date;
  endTime!: Date;
  selectedTime!: string;

  appointments!: Array<Appointments>;
  selectedTimeSlots: Array<number> = [];
  selectedTimeSlotsWhenFilterIsOpen: Array<number> = [];

  showInstructorDetailsFlag = false;
  isTimeSlotFilterOpen = false;
  isTimeSlotsAvailable = false;

  filterTimeSlotPlaceholder = 'Filter Time Slot';

  @Output() scheduleAppointmentsDetails = new EventEmitter<SlotOrStaffDetails>();
  @Output() toggleInstructorSideNav = new EventEmitter<boolean>();

  constructor(private readonly introductoryLessonService: IntroductoryLessonsService, private readonly cdr: ChangeDetectorRef) {
    super();
  }

  ngOnInit(): void {
    this.startDate = this.getWeekStartDate();
    this.endDate = this.getWeekEndDate();
    this.getIntroductoryLessonDetails();
  }

  // getIntroductoryLessonDetails(selectedInstructorsId?: Array<number>): void {
  //   this.showPageLoader = true;
  //   this.introductoryLessonService
  //     .getList<CBResponse<Appointments>>(this.getIntroductoryLessonEndPoint(selectedInstructorsId))
  //     .pipe(takeUntil(this.destroy$))
  //     .subscribe({
  //       next: (res: CBResponse<Appointments>) => {
  //         this.appointments = res.result.items;
  //         this.showAppointmentDetailsByDefault();
  //         this.checkIsEveryClassSlotsInListAreEmpty();
  //         this.showPageLoader = false;
  //         this.cdr.detectChanges();
  //       },
  //       error: () => {
  //         this.showPageLoader = false;
  //         this.cdr.detectChanges();
  //       }
  //     });
  // }
  setTimeFilter(event: any): void {
    this.selectedTime = event.valueText;
    [this.startTime, this.endTime] = event.value;
    this.getIntroductoryLessonDetails();
  }

  getIntroductoryLessonDetails(selectedInstructorsId?: Array<number>): void {
    // const instructorIds = selectedInstructorsId ?? this.selectedInstructorsIdFromParent
    this.showPageLoader = true;
    this.introductoryLessonService
      .add(
        {
          scheduleStartDate: DateUtils.getUtcRangeForLocalDate(this.startDate).startUtc,
          scheduleEndDate: DateUtils.getUtcRangeForLocalDate(this.endDate).endUtc,
          locationId: this.scheduleInfo?.locationId,
          instrumentId: this.scheduleInfo?.instrumentId,
          duration: Duration.THIRTY,
          instructorIds: selectedInstructorsId,
          isIntroductoryClassAvailable: true,
          startTimeFilter: CommonUtils.combineDateAndTime(this.startDate.toDateString(), this.startTime?.toISOString()),
          endTimeFilter: CommonUtils.combineDateAndTime(this.endDate.toDateString(), this.endTime?.toISOString()),
          studentId: 0
        },
        API_URL.introductoryLesson.getAllAvailableIntroductoryLessons
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Appointments>) => {
          this.appointments = res.result.items;
          this.showAppointmentDetailsByDefault();
          this.checkIsEveryClassSlotsInListAreEmpty();
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getIntroductoryLessonEndPoint(selectedInstructorsId?: Array<number>): string {
    const createFilter = (key: string, value: any) => (value ? `&${key}=${value}` : '');

    const { childAge, skill, lessonType, instrumentId, subInstrumentId, locationId } = this.scheduleInfo;
    const startDateFilter = this.startDate ? `StartDateFilter=${moment(this.startDate).format('MM/DD/YYYY')}` : '';
    const endDateFilter = this.endDate ? `&EndDateFilter=${moment(this.endDate).format('MM/DD/YYYY')}` : '';
    const durationFilter = this.selectedTimeSlots?.length ? `&TimeInterval=${this.selectedTimeSlots.join(',')}` : '';
    const instructorIdFilter = selectedInstructorsId?.length ? `&InstructorIdFilter=${selectedInstructorsId.join(',')}` : '';
    const IsVirtualClassFilter = `&IsVirtualClassFilter=${lessonType}`;

    return (
      `${API_URL.crud.getAll}?${startDateFilter}${endDateFilter}${durationFilter}${instructorIdFilter}` +
      `${createFilter('AgeGroupFilter', childAge)}` +
      `${createFilter('SkillFilter', skill)}` +
      `${IsVirtualClassFilter}` +
      `${createFilter('InstrumentIdFilter', instrumentId)}` +
      `${createFilter('SubInstrumentIdFilter', subInstrumentId)}` +
      `${createFilter('LocationIdFilter', locationId)}`
    );
  }

  showAppointmentDetailsByDefault(): void {
    if (this.appointments.length) {
      const { introductoryLesson } = this.appointments[0];
      if (introductoryLesson?.length) {
        const firstSlot = introductoryLesson[0];
        this.scheduleAppointmentsDetails.emit({ slotDetails: firstSlot, showStaffDetails: false });
        this.selectedTimeSlotId = firstSlot.id;
      }
      return;
    }
    this.scheduleAppointmentsDetails.emit({ slotDetails: undefined, showStaffDetails: false });
  }

  applyTimeSlotFilter(): void {
    this.getIntroductoryLessonDetails();
    this.isTimeSlotFilterOpen = false;
    this.setFilterSlotPlaceholder();
  }

  setFilterSlotPlaceholder(): void {
    this.filterTimeSlotPlaceholder = this.selectedTimeSlots.length
      ? `${(this.constants.scheduleTimeSlotsLabels as any)[this.selectedTimeSlots[0]]}` +
        (this.selectedTimeSlots.length > 1 ? `  ${this.selectedTimeSlots.length - 1}+` : '')
      : 'Filter Time Slot';
  }

  toggleTimeSlotFilter(isOpen: boolean): void {
    this.isTimeSlotFilterOpen = isOpen;
    if (isOpen) {
      this.selectedTimeSlotsWhenFilterIsOpen = JSON.parse(JSON.stringify(this.selectedTimeSlots));
    } else {
      this.selectedTimeSlots = this.selectedTimeSlotsWhenFilterIsOpen;
    }
  }

  getWeekStartDate(): Date {
    const currentDate = new Date();
    const currentDay = currentDate.getDay();
    const diff = currentDate.getDate() - currentDay + (currentDay === 0 ? -6 : 1);
    return new Date(currentDate.setDate(diff));
  }

  getWeekEndDate(): Date {
    const currentDate = new Date();
    const currentDay = currentDate.getDay();
    const diff = currentDate.getDate() - currentDay + (currentDay === 0 ? 0 : 7);
    return new Date(currentDate.setDate(diff));
  }

  setSelectedSlotIdAndShowInstructorDetailsFlag(slotDetails: IntroductoryLesson, flag: boolean, event: MouseEvent): void {
    if (flag) {
      event.stopPropagation();
    }
    this.selectedTimeSlotId = slotDetails.id;
    this.showInstructorDetailsFlag = flag;
    this.scheduleAppointmentsDetails.emit({ slotDetails, showStaffDetails: flag });
  }

  isTimeSlotPresentInSelectedTimeSlots(slotId: number): boolean {
    return this.selectedTimeSlots?.includes(slotId);
  }

  addOrRemoveTimeSlot(slotId: number): void {
    if (this.selectedTimeSlots?.includes(slotId)) {
      this.selectedTimeSlots = this.selectedTimeSlots.filter(id => id !== slotId);
      return;
    }
    this.selectedTimeSlots.push(slotId);
  }

  checkIsEveryClassSlotsInListAreEmpty(): void {
    this.isTimeSlotsAvailable = this.appointments?.every((item: Appointments) => item.introductoryLesson.length === 0);
  }

  openInstructorSideNav(isOpen: boolean): void {
    this.toggleInstructorSideNav.emit(isOpen);
  }
}
