<div class="launch-page-container">
    <div class="image-wrapper">
        <img class="octopus-logo" [src]="constants.applicationLogoUrl" alt="">
    </div>
    <div class="content-wrapper">
        <div class="main-content">
            <h2 class="main-title">{{ isCountdownComplete ? 'Welcome to SQUID!' : 'Striking a Chord Soon!' }}</h2>
            <p class="subtitle">
                @if (isCountdownComplete) {
                    Our music school's new platform, <b>SQUID</b>, is now live! You can now manage your lessons at OMS with ease. Start exploring now.
                } @else {
                    Our music school's new platform, <b>SQUID</b>, is launching soon and managing your lessons at OMS is about to get a whole lot easier! Stay tuned.
                }
            </p>

            <div class="countdown-section">
                <div class="countdown-labels">
                    <span class="countdown-label">Days</span>
                    <span class="countdown-label">Hours</span>
                    <span class="countdown-label">Minutes</span>
                    <span class="countdown-label">Seconds</span>
                </div>
                <div class="countdown-timer">
                    <span class="countdown-number">{{ days }}</span>
                    <span class="countdown-separator">:</span>
                    <span class="countdown-number">{{ hours }}</span>
                    <span class="countdown-separator">:</span>
                    <span class="countdown-number">{{ minutes }}</span>
                    <span class="countdown-separator">:</span>
                    <span class="countdown-number primary-color">{{ seconds }}</span>
                </div>
            </div>

            <!-- to be used -->
            <!-- <p class="launch-message">Coming soon...</p> -->

            @if (isCountdownComplete) {
                <div class="action-buttons">
                    <button mat-raised-button color="accent" class="mat-accent-btn act-btn" type="button" (click)="onSignUp()">
                        Sign Up
                    </button>
                    <button mat-raised-button color="primary" class="mat-primary-btn act-btn" type="button" (click)="onSignIn()">
                        Sign In
                    </button>
                </div>
            }
            @else {
                <div class="action-buttons">
                    <button mat-raised-button color="accent" class="mat-accent-btn act-btn" type="button" (click)="onScheduleIntroductoryLesson()">
                        Schedule an Introductory Lesson
                    </button>
                    <button mat-raised-button color="primary" class="mat-primary-btn act-btn" type="button" (click)="onResetPassword()">
                        Need to Reset Password?
                    </button>
                </div>
            }
        </div>
    </div>
</div>