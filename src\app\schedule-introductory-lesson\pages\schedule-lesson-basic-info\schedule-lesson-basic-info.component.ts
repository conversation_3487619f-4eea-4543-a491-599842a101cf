import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { RouterModule } from '@angular/router';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';

@Component({
  selector: 'app-schedule-lesson-basic-info',
  standalone: true,
  imports: [RouterModule, CommonModule],
  templateUrl: './schedule-lesson-basic-info.component.html',
  styleUrl: './schedule-lesson-basic-info.component.scss'
})
export class ScheduleLessonBasicInfoComponent extends BaseComponent {
  @Input() isFromLaunchPage!: boolean;

  constructor() {
    super();
  }
}
