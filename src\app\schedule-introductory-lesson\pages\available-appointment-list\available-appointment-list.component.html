<div class="available-appointment-list-wrapper mt-20">
  <div class="filter-wrapper">
    <div class="date-time-slot-wrapper">
      <div class="date-range">
        <mat-form-field>
          <mat-date-range-input [rangePicker]="picker" (click)="picker.open()">
            <input matStartDate placeholder="Start date" [(ngModel)]="startDate" (click)="picker.open()" />
            <input matEndDate placeholder="End date" [(ngModel)]="endDate" (click)="picker.open()" />
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker (closed)="getIntroductoryLessonDetails()"></mat-date-range-picker>
        </mat-form-field>
      </div>
      <!-- <div
        class="select-instructor-wrapper time-slot-range"
        (click)="toggleTimeSlotFilter(!isTimeSlotFilterOpen)"
        type="button"
        cdkOverlayOrigin
        #trigger="cdkOverlayOrigin">
        <div>
          <span class="select-instructor-btn">{{ filterTimeSlotPlaceholder }}</span>
        </div>
        <mat-icon>keyboard_arrow_down</mat-icon>
      </div> -->
      <div class="time-range">
        <input matInput [(ngModel)]="selectedTime" (click)="timePicker.open()" placeholder="Select Time Slot" />
        <mbsc-datepicker
          theme="ios"
          themeVariant="light"
          [ariaHidden]="false"
          [controls]="['time']"
          select="range"
          display="anchored"
          [touchUi]="true"
          (onChange)="setTimeFilter($event)"
          #timePicker></mbsc-datepicker>
      </div>
    </div>
    <div class="select-instructor-wrapper" (click)="openInstructorSideNav(true)">
      <div>
        <span class="select-instructor-btn">Select Instructor</span>:
        <span class="selected-instructor-count">{{ selectedInstructorsCount }}</span>
      </div>
      <mat-icon>keyboard_arrow_right</mat-icon>
    </div>
  </div>
  <div class="appointment-list-wrapper">
    <div class="date-header-wrapper">
      @for (item of dateRange; track $index) {
        <div
          class="header-content"
          [ngClass]="{ 'schedule-date-active': item.date === selectedDate }"
          (click)="setScheduleDate(item.date)">
          <div class="date">{{ item?.date | date: "mediumDate" }}</div>
          <div class="day">{{ item?.date | date: constants.dateFormats.day }}</div>
        </div>
      }
    </div>
    <ng-container [ngTemplateOutlet]="showPageLoader ? showLoader : appointmentContent"></ng-container>
  </div>
</div>

<ng-template #appointmentContent>
  <ng-container [ngTemplateOutlet]="isTimeSlotsAvailable ? noAppointmentsAvailable : appointmentsList"></ng-container>
</ng-template>

<ng-template #appointmentsList>
  <div class="time-slot-wrapper">
    @for (item of appointments; track $index) {
      <div class="time-slot-content-wrapper">
        @for (slots of item.introductoryLessonsDetails; track $index) {
          <div
            [ngClass]="{ 'time-slot-content': true, 'time-slot-content-active': slots.id === selectedTimeSlotId }"
            (click)="setSelectedSlotIdAndShowInstructorDetailsFlag(slots, false, $event)">
            <div class="time-slot">{{ slots.startTime | date: "hh:mm a" }} - {{ slots.endTime | date: "hh:mm a" }}</div>
            <div class="staff-wrapper" (click)="setSelectedSlotIdAndShowInstructorDetailsFlag(slots, true, $event)">
              <img [src]="constants.staticImages.images.profileImgPlaceholder" alt="" class="staff-img" />
              <div class="staff-name">{{ slots.instructorName }}</div>
            </div>
          </div>
        }
      </div>
    }
  </div>
</ng-template>

<ng-template #noAppointmentsAvailable>
  <div class="no-appointments-available-wrapper">
    <div class="no-appointments-available-content">
      <div class="description">
        @if (selectedTimeSlots.length) {
          There is <span class="d-bold">no instructor available</span> for selected time slots, so please select another
          time slot.
        } @else {
          There is <span class="d-bold">no instructor available</span> for selected date range, so please select another
          date range.
        }
      </div>
      <!-- @if (selectedTimeSlots.length) {
        <div class="NAAW-time-slots-wrapper">
          <div class="time-slot-options">
            <div
              [ngClass]="{
                'time-slot': true,
                'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.nineToElevenAM)
              }"
              (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.nineToElevenAM)">
              9:00 AM - 11:00 AM
            </div>
            <div
              [ngClass]="{
                'time-slot': true,
                'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.elevenToOnePM)
              }"
              (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.elevenToOnePM)">
              11:00 AM - 1:00 PM
            </div>
            <div
              [ngClass]="{
                'time-slot': true,
                'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.oneToThreePM)
              }"
              (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.oneToThreePM)">
              1:00 PM - 3:00 PM
            </div>
          </div>
          <div class="time-slot-options">
            <div
              [ngClass]="{
                'time-slot': true,
                'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.threeToFivePM)
              }"
              (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.threeToFivePM)">
              3:00 PM - 5:00 PM
            </div>
            <div
              [ngClass]="{
                'time-slot': true,
                'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.fiveToSevenPM)
              }"
              (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.fiveToSevenPM)">
              5:00 PM - 7:00 PM
            </div>
            <div
              [ngClass]="{
                'time-slot': true,
                'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.sevenToNinePM)
              }"
              (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.sevenToNinePM)">
              7:00 PM - 9:00 PM
            </div>
          </div>
        </div>
        <button
          mat-raised-button
          color="primary"
          class="mat-primary-btn mt-20"
          type="button"
          (click)="applyTimeSlotFilter()">
          Select
        </button>
      } -->
    </div>
  </div>
</ng-template>

<!-- <ng-template cdkConnectedOverlay [cdkConnectedOverlayOrigin]="trigger" [cdkConnectedOverlayOpen]="isTimeSlotFilterOpen">
  <div class="time-slots-wrapper">
    <div class="time-slot-content-wrapper">
      <div class="time-slot-content">
        <div
          [ngClass]="{
            'time-slot': true,
            'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.nineToElevenAM)
          }"
          (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.nineToElevenAM)">
          9:00 AM - 11:00 AM
        </div>
        <div
          [ngClass]="{
            'time-slot': true,
            'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.elevenToOnePM)
          }"
          (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.elevenToOnePM)">
          11:00 AM - 1:00 PM
        </div>
      </div>
      <div class="time-slot-content">
        <div
          [ngClass]="{
            'time-slot': true,
            'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.oneToThreePM)
          }"
          (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.oneToThreePM)">
          1:00 PM - 3:00 PM
        </div>
        <div
          [ngClass]="{
            'time-slot': true,
            'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.threeToFivePM)
          }"
          (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.threeToFivePM)">
          3:00 PM - 5:00 PM
        </div>
      </div>
      <div class="time-slot-content">
        <div
          [ngClass]="{
            'time-slot': true,
            'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.fiveToSevenPM)
          }"
          (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.fiveToSevenPM)">
          5:00 PM - 7:00 PM
        </div>
        <div
          [ngClass]="{
            'time-slot': true,
            'time-slot-active': isTimeSlotPresentInSelectedTimeSlots(constants.scheduleTimeSlots.sevenToNinePM)
          }"
          (click)="addOrRemoveTimeSlot(constants.scheduleTimeSlots.sevenToNinePM)">
          7:00 PM - 9:00 PM
        </div>
      </div>
    </div>
    <div class="action-btn-wrapper">
      <div class="action-btn" (click)="toggleTimeSlotFilter(false)">Cancel</div>
      <div class="action-btn" (click)="applyTimeSlotFilter()">Apply</div>
    </div>
  </div>
</ng-template> -->

<ng-template #showLoader>
  <app-content-loader></app-content-loader>
</ng-template>
